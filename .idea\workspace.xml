<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="963229b3-2d41-4c23-bc28-7efc928f0ada" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="2eGfdF7KoDZttL923xiKMiSQJuo" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="ToolWindowRun.ShowToolbar" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="loss" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ultralytics-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ultralytics/utils" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ultralytics/utils/loss.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ultralytics-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="plot-method-2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ultralytics-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/plot-method-2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="plot-version-2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ultralytics-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/plot-version-2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="plot" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ultralytics-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/plot.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.plot" />
        <item itemvalue="Python.plot-method-2" />
        <item itemvalue="Python.loss" />
        <item itemvalue="Python.plot-version-2" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="963229b3-2d41-4c23-bc28-7efc928f0ada" name="Default Changelist" comment="" />
      <created>1711535921973</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1711535921973</updated>
    </task>
    <servers />
  </component>
  <component name="WindowStateProjectService">
    <state x="437" y="232" key="#com.intellij.fileTypes.FileTypeChooser" timestamp="1751970803736">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="437" y="232" key="#com.intellij.fileTypes.FileTypeChooser/0.0.1536.864@0.0.1536.864" timestamp="1751970803736" />
    <state x="524" y="134" key="#xdebugger.evaluate" timestamp="1751034025081">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="524" y="134" key="#xdebugger.evaluate/0.0.1536.864@0.0.1536.864" timestamp="1751034025081" />
    <state width="1534" height="440" key="DebuggerActiveHint" timestamp="1751965259026">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1534" height="440" key="DebuggerActiveHint/0.0.1536.864@0.0.1536.864" timestamp="1751965259026" />
    <state x="-1600" y="175" key="FileDocumentManager.FileCacheConflict" timestamp="1752303197026">
      <screen x="-1600" y="175" width="1600" height="900" />
    </state>
    <state x="-1600" y="175" key="FileDocumentManager.FileCacheConflict/0.0.1536.864/-1600.175.1600.900@-1600.175.1600.900" timestamp="1752303197026" />
    <state width="1515" height="285" key="GridCell.Tab.0.bottom" timestamp="1754122161677">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1579" height="272" key="GridCell.Tab.0.bottom/0.0.1536.864/-1600.175.1600.900@-1600.175.1600.900" timestamp="1752413540346" />
    <state width="1515" height="285" key="GridCell.Tab.0.bottom/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1754122161677" />
    <state width="1515" height="305" key="GridCell.Tab.0.bottom/0.0.1536.864@0.0.1536.864" timestamp="1752494121849" />
    <state width="1515" height="285" key="GridCell.Tab.0.center" timestamp="1754122161677">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1579" height="272" key="GridCell.Tab.0.center/0.0.1536.864/-1600.175.1600.900@-1600.175.1600.900" timestamp="1752413540345" />
    <state width="1515" height="285" key="GridCell.Tab.0.center/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1754122161677" />
    <state width="1515" height="305" key="GridCell.Tab.0.center/0.0.1536.864@0.0.1536.864" timestamp="1752494121849" />
    <state width="1515" height="285" key="GridCell.Tab.0.left" timestamp="1754122161677">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1579" height="272" key="GridCell.Tab.0.left/0.0.1536.864/-1600.175.1600.900@-1600.175.1600.900" timestamp="1752413540344" />
    <state width="1515" height="285" key="GridCell.Tab.0.left/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1754122161677" />
    <state width="1515" height="305" key="GridCell.Tab.0.left/0.0.1536.864@0.0.1536.864" timestamp="1752494121848" />
    <state width="1515" height="285" key="GridCell.Tab.0.right" timestamp="1754122161677">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1579" height="272" key="GridCell.Tab.0.right/0.0.1536.864/-1600.175.1600.900@-1600.175.1600.900" timestamp="1752413540345" />
    <state width="1515" height="285" key="GridCell.Tab.0.right/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1754122161677" />
    <state width="1515" height="305" key="GridCell.Tab.0.right/0.0.1536.864@0.0.1536.864" timestamp="1752494121849" />
    <state width="1515" height="288" key="GridCell.Tab.1.bottom" timestamp="1753805667855">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1515" height="288" key="GridCell.Tab.1.bottom/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1753805667855" />
    <state width="1515" height="288" key="GridCell.Tab.1.bottom/0.0.1536.864@0.0.1536.864" timestamp="1751966527737" />
    <state width="1515" height="288" key="GridCell.Tab.1.center" timestamp="1753805667855">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1515" height="288" key="GridCell.Tab.1.center/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1753805667855" />
    <state width="1515" height="288" key="GridCell.Tab.1.center/0.0.1536.864@0.0.1536.864" timestamp="1751966527737" />
    <state width="1515" height="288" key="GridCell.Tab.1.left" timestamp="1753805667854">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1515" height="288" key="GridCell.Tab.1.left/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1753805667854" />
    <state width="1515" height="288" key="GridCell.Tab.1.left/0.0.1536.864@0.0.1536.864" timestamp="1751966527737" />
    <state width="1515" height="288" key="GridCell.Tab.1.right" timestamp="1753805667855">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="1515" height="288" key="GridCell.Tab.1.right/0.0.1536.864/-1600.175.1600.900@0.0.1536.864" timestamp="1753805667855" />
    <state width="1515" height="288" key="GridCell.Tab.1.right/0.0.1536.864@0.0.1536.864" timestamp="1751966527737" />
    <state x="214" y="85" key="SettingsEditor" timestamp="1751107149863">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="214" y="85" key="SettingsEditor/0.0.1536.864@0.0.1536.864" timestamp="1751107149863" />
    <state width="775" height="439" key="XDebugger.FullValuePopup" timestamp="1751472808145">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state width="775" height="439" key="XDebugger.FullValuePopup/0.0.1536.864@0.0.1536.864" timestamp="1751472808145" />
    <state x="323" y="161" key="com.intellij.xdebugger.impl.breakpoints.ui.BreakpointsDialogFactory$2" timestamp="1751032626071">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="323" y="161" key="com.intellij.xdebugger.impl.breakpoints.ui.BreakpointsDialogFactory$2/0.0.1536.864@0.0.1536.864" timestamp="1751032626071" />
    <state x="468" y="30" width="600" height="804" key="find.popup" timestamp="1751032920550">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="468" y="30" width="600" height="804" key="find.popup/0.0.1536.864@0.0.1536.864" timestamp="1751032920550" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="pred_scores_teacher" />
        <watch expression="fg_mask_teacher" />
        <watch expression="target_scores_teacher" />
        <watch expression="batch_size_teacher" />
        <watch expression="pred_distri_teacher" />
        <watch expression="pred_bboxes_teacher" />
        <watch expression="target_bboxes_teacher" />
        <watch expression="target_ltrb_teacher" />
        <watch expression="pred_scores_student" />
        <watch expression="fg_mask_student" />
        <watch expression="target_scores_student" />
        <watch expression="batch_size_studnet" />
        <watch expression="pred_distri_student" />
        <watch expression="pred_bboxes_student" language="Python" />
        <watch expression="target_bboxes_student" />
        <watch expression="target_ltrb_student" />
        <watch expression="fg_mask_distill" language="Python" />
        <watch expression="distill_relative_teacher" />
        <watch expression="distill_relative_student" />
        <watch expression="fg_mask_distill_1" />
        <watch expression="fg_mask_distill_2" />
        <watch expression="pred_scores_teacher_sig" />
        <watch expression="distill_loss_cls_3_1" />
        <watch expression="target_scores_distill_sum" />
        <watch expression="target_scores_distill_1" />
        <watch expression="keepdim" />
        <watch expression="target_dfl_distill_3_2" language="Python" />
        <watch expression="distill_loss_dfl_3_" />
        <watch expression="target_scores_student_3_2" />
        <watch expression="target_scores_distill_4" />
        <watch expression="pred" />
        <watch expression="target_scores_sum" language="Python" />
        <watch expression="weight_box_3" />
        <watch expression="shape" />
        <watch expression="distill_loss_dfl_3_1" />
        <watch expression="distill_loss_dfl_3_2" language="Python" />
        <watch expression="pred_distri_student_3_1" />
        <watch expression="target_scores_distill_1.sum()" language="Python" />
        <watch expression="fg_mask_distill" language="Python" />
        <watch expression="fg_mask_distill.H" />
        <watch expression="pred_distri_student[fg_mask_distill]." language="Python" />
      </configuration>
    </watches-manager>
  </component>
</project>